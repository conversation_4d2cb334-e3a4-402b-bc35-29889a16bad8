from dataclasses import dataclass
from datetime import datetime
from typing import Optional
import pandas as pd

@dataclass
class ProductItem:
    item_code: str
    item_description: str
    department: str
    retail: float
    sale_price: float
    rrp: Optional[float]
    modified_date: datetime
    alias: Optional[str] = None

    @classmethod
    def from_df_row(cls, row):
        """從 DataFrame 行創建 ProductItem"""
        return cls(
            item_code=str(row['Item Code']),
            item_description=str(row['Item Description']),
            department=str(row['Department']),
            retail=float(row['Retail']),
            sale_price=float(row['Sale Price']),
            rrp=float(row['RRP']) if 'RRP' in row and pd.notnull(row['RRP']) else None,
            modified_date=pd.to_datetime(row['Modified']),
            alias=str(row['Alias']) if '<PERSON>as' in row and pd.notnull(row['Alias']) else None
        )

@dataclass
class LabelSettings:
    font_sizes: dict
    text_offsets: dict
    barcode_options: dict
