import tkinter as tk
from tkinter import ttk
from datetime import datetime, timedelta
from tkcalendar import DateEntry
from utils.logger import Logger

class SearchPanel:
    def __init__(self, parent, excel_handler, search_callback):
        self.parent = parent
        self.excel_handler = excel_handler
        self.search_callback = search_callback
        self.logger = Logger()
        self.search_timer = None
        self.auto_print = False
        
        self.create_widgets()

    def create_widgets(self):
        """創建搜索區域組件"""
        # 主搜索框架
        search_frame = tk.Frame(self.parent, bg='white')
        search_frame.pack(fill=tk.X, padx=5, pady=5)
        
        # 主搜索標籤和輸入框
        tk.Label(
            search_frame,
            text="請輸入商品代碼：",
            font=("Arial", 10),
            bg='white'
        ).pack(side=tk.LEFT)
        
        self.search_entry = ttk.Entry(search_frame, width=30)
        self.search_entry.pack(side=tk.LEFT, padx=5)
        self.search_entry.bind('<KeyRelease>', self.on_search_key_release)
        
        # 隱藏的備份輸入框
        self.hidden_search_entry = ttk.Entry(search_frame)
        self.hidden_search_entry.pack_forget()
        
        # Excel 預覽搜索框架
        excel_search_frame = tk.Frame(self.parent, bg='white')
        excel_search_frame.pack(fill=tk.X, padx=5, pady=5)
        
        tk.Label(
            excel_search_frame,
            text="Excel 搜索：",
            font=("Arial", 10),
            bg='white'
        ).pack(side=tk.LEFT)
        
        self.excel_search_entry = ttk.Entry(excel_search_frame)
        self.excel_search_entry.pack(side=tk.LEFT, fill=tk.X, expand=True)
        
        # 日期過濾框架
        date_frame = tk.Frame(self.parent, bg='white')
        date_frame.pack(fill=tk.X, padx=5, pady=5)
        
        tk.Label(
            date_frame,
            text="日期範圍：",
            font=("Arial", 10),
            bg='white'
        ).pack(side=tk.LEFT)
        
        # 開始日期選擇器
        self.start_date = DateEntry(
            date_frame,
            width=12,
            background='darkblue',
            foreground='white',
            borderwidth=2,
            date_pattern='yyyy-mm-dd'
        )
        self.start_date.pack(side=tk.LEFT, padx=2)
        
        tk.Label(
            date_frame,
            text="到",
            font=("Arial", 10),
            bg='white'
        ).pack(side=tk.LEFT, padx=2)
        
        # 結束日期選擇器
        self.end_date = DateEntry(
            date_frame,
            width=12,
            background='darkblue',
            foreground='white',
            borderwidth=2,
            date_pattern='yyyy-mm-dd'
        )
        self.end_date.pack(side=tk.LEFT, padx=2)
        
        # 清除日期按鈕
        ttk.Button(
            date_frame,
            text="清除日期過濾",
            command=self.clear_date_filter
        ).pack(side=tk.LEFT, padx=5)

        # 綁定日期選擇事件
        self.start_date.bind("<<DateEntrySelected>>", self.apply_date_filter)
        self.end_date.bind("<<DateEntrySelected>>", self.apply_date_filter)

    def on_search_key_release(self, event):
        """處理搜索框按鍵釋放事件"""
        if self.search_timer:
            self.parent.after_cancel(self.search_timer)
        self.search_timer = self.parent.after(500, self.perform_search)

    def perform_search(self):
        """執行搜索"""
        try:
            search_text = self.search_entry.get().strip()
            self.logger.info(f"執行搜索: {search_text}")
            
            if not search_text:
                return
            
            # 獲取日期範圍
            date_range = (
                self.start_date.get_date(),
                self.end_date.get_date() + timedelta(days=1) - timedelta(microseconds=1)
            )
            
            # 執行搜索
            results = self.excel_handler.search_items(search_text, date_range)
            
            if results:
                self.search_callback(results[0])  # 使用第一個結果
                self.logger.info("搜索成功")
            else:
                self.logger.info("未找到匹配項")
                self.search_callback(None)
                
        except Exception as e:
            self.logger.error(f"搜索時發生錯誤: {str(e)}")
            self.search_callback(None)

    def apply_date_filter(self, event=None):
        """應用日期過濾"""
        self.perform_search()

    def clear_date_filter(self):
        """清除日期過濾"""
        # 設置為最大範圍
        self.start_date.set_date(self.excel_handler.df['Modified'].min())
        self.end_date.set_date(self.excel_handler.df['Modified'].max())
        self.perform_search()

    def focus_search(self):
        """聚焦到搜索框"""
        self.search_entry.focus_set()

    def clear_search(self):
        """清空搜索框"""
        self.search_entry.delete(0, tk.END)
        self.focus_search()

    def on_search(self, event=None):
        """處理搜索"""
        search_text = self.search_entry.get().strip()
        if search_text:
            item_data = self.excel_handler.search_item(search_text)
            self.search_callback(item_data)

    def set_auto_print(self, enabled):
        """設置自動列印"""
        self.auto_print = enabled
