import pandas as pd
import os
import glob
from datetime import datetime, timedelta
from models.data_models import ProductItem
from utils.logger import Logger

class ExcelHandler:
    def __init__(self, config_manager, logger=None):
        self.config = config_manager
        self.logger = logger or Logger()
        self.df = None
        self.original_df = None
        self.current_excel_path = None

    def load_latest_excel(self):
        """載入最新的 Excel 文件"""
        try:
            excel_dir = self.config.get_excel_directory()
            self.logger.info(f"正在從目錄載入 Excel: {excel_dir}")
            
            if not os.path.exists(excel_dir):
                raise FileNotFoundError(f"Excel 目錄不存在：{excel_dir}")
            
            excel_files = glob.glob(os.path.join(excel_dir, "*.xlsx"))
            if not excel_files:
                raise FileNotFoundError(f"在 {excel_dir} 中找不到 Excel 文件")
            
            latest_excel = max(excel_files, key=os.path.getmtime)
            return self.load_excel(latest_excel)
            
        except Exception as e:
            self.logger.error(f"載入最新 Excel 時發生錯誤: {str(e)}")
            raise

    def load_excel(self, file_path):
        """載入指定的 Excel 文件"""
        try:
            self.logger.info(f"正在載入 Excel 文件: {file_path}")
            df = pd.read_excel(file_path)
            
            if self._validate_dataframe(df):
                self.df = df
                self.original_df = df.copy()
                self.current_excel_path = file_path
                return True
            return False
            
        except Exception as e:
            self.logger.error(f"載入 Excel 文件時發生錯誤: {str(e)}")
            raise

    def _validate_dataframe(self, df):
        """驗證 DataFrame 是否包含所需列"""
        required_columns = [
            'Item Code', 'Item Description', 'Department',
            'Sale Price', 'Retail', 'Modified'
        ]
        missing_columns = [col for col in required_columns if col not in df.columns]
        if missing_columns:
            self.logger.error(f"Excel 缺少必要列: {missing_columns}")
            return False
        return True

    def search_items(self, search_text, date_range=None):
        """搜索商品"""
        try:
            if self.df is None:
                return []

            # 複製當前數據
            filtered_df = self.df.copy()

            # 應用日期過濾
            if date_range:
                start_date, end_date = date_range
                filtered_df = filtered_df[
                    (filtered_df['Modified'] >= start_date) &
                    (filtered_df['Modified'] <= end_date)
                ]

            # 應用搜索條件
            if search_text:
                search_text = str(search_text).lower()
                mask = filtered_df.apply(
                    lambda row: any(
                        str(value).lower().find(search_text) != -1
                        for value in row.values if pd.notnull(value)
                    ),
                    axis=1
                )
                filtered_df = filtered_df[mask]

            return [ProductItem.from_df_row(row) for _, row in filtered_df.iterrows()]

        except Exception as e:
            self.logger.error(f"搜索時發生錯誤: {str(e)}")
            return []
