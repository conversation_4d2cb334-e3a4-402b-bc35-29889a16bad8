# 設置保存功能使用指南

## 📋 **功能概述**

標籤生成器現在支援**自動保存調整設置**，這意味著：
- ✅ 您對標籤的所有調整都會自動保存到配置文件
- ✅ 下次啟動應用程序時，會自動載入您上次的設置
- ✅ 不同部門（Ethicals vs Non-Ethicals）的設置分別保存
- ✅ 無需手動保存，所有調整即時生效並保存

## 🎯 **支援的調整項目**

### **Ethicals 產品設置**
當選擇 Ethicals 部門的產品時，可調整：
- 條碼位置 (Y軸)
- 文字位置 (X軸、Y軸)
- 字體大小

### **Non-Ethicals 產品設置**
當選擇 Non-Ethicals 部門的產品時，可調整更多項目：

#### 🏷️ **條碼設置**
- 條碼位置 (X軸、Y軸)
- 條碼寬度比例
- 條碼高度比例

#### 📝 **商品描述設置**
- 描述位置 (X軸、Y軸)
- 描述字體大小

#### 💰 **零售價設置**
- 零售價位置 (X軸、Y軸)
- 零售價字體大小

#### 🏪 **RRP 設置**
- RRP 位置 (X軸、Y軸)
- RRP 字體大小

#### 📅 **日期設置**
- 日期位置 (X軸、Y軸)
- 日期字體大小

#### 🔢 **商品代碼設置**
- 商品代碼位置 (X軸、Y軸)
- 商品代碼字體大小

## 🔧 **如何使用**

### **步驟 1：選擇產品**
1. 在搜索框中輸入產品代碼或名稱
2. 選擇要調整的產品
3. 系統會自動顯示對應部門的調整控制項

### **步驟 2：調整設置**
1. 使用右側的調整控制項（Spinbox）
2. 即時預覽調整效果
3. **設置會自動保存** - 無需手動保存

### **步驟 3：驗證保存**
1. 關閉應用程序
2. 重新啟動應用程序
3. 選擇相同部門的產品
4. 確認調整值已經載入

## 📁 **配置文件位置**

設置保存在：`ticket_generator/config.txt`

### **配置文件結構**
```ini
[font_sizes_ethicals]          # Ethicals 字體大小
[text_offsets_ethicals]        # Ethicals 文字位置
[barcode_options_ethicals]     # Ethicals 條碼設置

[font_sizes]                   # Non-Ethicals 字體大小
[text_offsets]                 # Non-Ethicals 文字位置
[barcode_options]              # Non-Ethicals 條碼設置
```

## 🚀 **最新改進**

### **Non-Ethical 標籤優化**
根據您的範例，已對 Non-Ethical 標籤進行以下改進：

1. **條碼改進**
   - ✅ 移除條碼下方的數字顯示
   - ✅ 增加條碼寬度
   - ✅ 優化條碼模組設置

2. **價格顯示**
   - ✅ 中間價格顯示最低價格（零售價）
   - ✅ 零售價使用加粗字體
   - ✅ RRP 格式改為 "RRP: $XX.XX"

3. **商品描述**
   - ✅ 支援多行換行顯示
   - ✅ 最多顯示兩行
   - ✅ 自動文字換行

4. **日期格式**
   - ✅ 改為 YYMMDD 格式（如：250107）

## 🔍 **故障排除**

### **設置沒有保存？**
1. 檢查 `config.txt` 文件是否存在
2. 確認應用程序有寫入權限
3. 查看控制台是否有錯誤訊息

### **設置沒有載入？**
1. 確認選擇了正確部門的產品
2. 檢查配置文件格式是否正確
3. 重新啟動應用程序

### **調整沒有效果？**
1. 確認已選擇產品
2. 檢查調整值是否在合理範圍內
3. 嘗試重新生成預覽

## 📞 **技術支援**

如果遇到問題：
1. 查看 `logs/` 目錄中的日誌文件
2. 檢查控制台輸出訊息
3. 備份並重置 `config.txt` 文件

---

**注意**：所有調整都會即時保存，無需手動保存操作。下次啟動應用程序時，您的設置會自動載入。
