import tkinter as tk
from tkinter import ttk
from PIL import ImageTk
import pandas as pd

class PreviewPanel:
    def __init__(self, parent, config_manager):
        self.parent = parent
        self.config = config_manager
        
        # 創建 Excel 預覽區域
        self.create_excel_preview()

    def create_excel_preview(self):
        """創建 Excel 預覽區域"""
        # Excel 預覽框架
        self.preview_frame = tk.LabelFrame(
            self.parent,
            text=" Excel 數據預覽 ",
            font=("Arial", 12, "bold"),
            bg='white',
            fg='#2c3e50'
        )
        self.preview_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        # 創建 Treeview
        self.tree = ttk.Treeview(
            self.preview_frame,
            columns=("Modified", "Item Description", "Item Code", "Sale Price", "Retail", "Alias"),
            show='headings',
            height=20
        )
        
        # 設置列標題
        columns_config = {
            "Modified": {"text": "Last Update", "width": 100},
            "Item Description": {"text": "Name", "width": 200},
            "Item Code": {"text": "Item Code", "width": 100},
            "Sale Price": {"text": "Sale Price", "width": 80},
            "Retail": {"text": "Retail", "width": 80},
            "Alias": {"text": "Alias", "width": 100}
        }
        
        # 設置每個列的標題
        for col, config in columns_config.items():
            self.tree.heading(col, text=config["text"])
            self.tree.column(col, width=config["width"])
        
        # 創建滾動條
        scrollbar = ttk.Scrollbar(
            self.preview_frame,
            orient=tk.VERTICAL,
            command=self.tree.yview
        )
        self.tree.configure(yscrollcommand=scrollbar.set)
        
        # 放置 Treeview 和滾動條
        self.tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

    def update_preview(self, df):
        """更新 Excel 預覽數據"""
        # 清空現有數據
        for item in self.tree.get_children():
            self.tree.delete(item)
        
        # 添加新數據
        if df is not None:
            for _, row in df.iterrows():
                try:
                    values = (
                        row['Modified'].strftime('%Y-%m-%d %H:%M'),
                        str(row['Item Description']),
                        str(row['Item Code']),
                        f"${float(row['Sale Price']):.2f}" if pd.notnull(row['Sale Price']) else '',
                        f"${float(row['Retail']):.2f}" if pd.notnull(row['Retail']) else '',
                        str(row.get('Alias', ''))
                    )
                    self.tree.insert('', 'end', values=values)
                except Exception as e:
                    print(f"處理行時發生錯誤: {str(e)}")
                    continue
