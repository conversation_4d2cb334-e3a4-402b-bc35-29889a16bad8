import tkinter as tk
from tkinter import ttk

class ControlsPanel:
    def __init__(self, parent, config_manager, printer_manager, print_callback):
        self.parent = parent
        self.config = config_manager
        self.printer_manager = printer_manager
        self.print_callback = print_callback
        
        # 列印開關變量
        self.print_enabled = tk.BooleanVar(value=False)
        
        self.create_widgets()

    def create_widgets(self):
        """創建控制區域組件"""
        # 主控制框架
        control_frame = tk.Frame(self.parent, bg='white')
        control_frame.pack(fill=tk.X, pady=(0, 10))
        
        # 列印開關
        print_check = ttk.Checkbutton(
            control_frame,
            text="自動列印標籤",
            variable=self.print_enabled,
            command=self.on_print_toggle
        )
        print_check.pack(side=tk.LEFT, padx=5)

    def on_print_toggle(self):
        """處理列印開關變更"""
        enabled = self.print_enabled.get()
        print(f"列印功能已{'啟用' if enabled else '禁用'}")
        self.print_callback(enabled)

    def is_print_enabled(self):
        """獲取列印開關狀態"""
        return self.print_enabled.get()
