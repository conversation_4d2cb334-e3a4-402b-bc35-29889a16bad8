import win32print
import win32ui
from PIL import Image, ImageWin
from utils.logger import Logger

class PrinterManager:
    def __init__(self, config_manager, logger=None):
        self.config = config_manager
        self.logger = logger or Logger()
        self.printer_name = self.config.get_printer_name()

    def print_label(self, image_or_path):
        """列印標籤"""
        try:
            self.logger.info(f"正在使用打印機 {self.printer_name} 列印標籤")

            # 處理輸入參數（可以是 PIL Image 或文件路徑）
            if isinstance(image_or_path, str):
                # 如果是文件路徑，載入圖像
                image = Image.open(image_or_path)
            else:
                # 如果是 PIL Image 對象
                image = image_or_path

            # 獲取打印機句柄
            hprinter = win32print.OpenPrinter(self.printer_name)

            try:
                # 創建打印機 DC
                hdc = win32ui.CreateDC()
                hdc.CreatePrinterDC(self.printer_name)

                # 開始打印工作
                hdc.StartDoc('Label')
                hdc.StartPage()

                # 將圖像轉換為設備相關的點陣圖
                dib = ImageWin.Dib(image)
                dib.draw(hdc.GetHandleOutput(), (0, 0, image.size[0], image.size[1]))

                # 結束打印
                hdc.EndPage()
                hdc.EndDoc()

                self.logger.info("標籤列印成功")

            finally:
                # 清理資源
                hdc.DeleteDC()
                win32print.ClosePrinter(hprinter)

        except Exception as e:
            self.logger.error(f"列印標籤時發生錯誤: {str(e)}")
            raise

    def get_available_printers(self):
        """獲取可用的打印機列表"""
        try:
            printers = win32print.EnumPrinters(2)
            return [printer[2] for printer in printers]
        except Exception as e:
            self.logger.error(f"獲取打印機列表時發生錯誤: {str(e)}")
            return []

    def set_printer(self, printer_name):
        """設置打印機"""
        self.printer_name = printer_name
        self.logger.info(f"已設置打印機: {printer_name}")
