import configparser
import os

class ConfigManager:
    def __init__(self):
        self.config_file = self._get_config_path()
        self.config = configparser.ConfigParser()
        self.load_config()

    def _get_config_path(self):
        """獲取配置文件的完整路徑"""
        current_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
        return os.path.join(current_dir, 'config.txt')

    def load_config(self):
        """載入配置文件"""
        if not os.path.exists(self.config_file):
            self._create_default_config()
        else:
            self.config.read(self.config_file, encoding='utf-8')
            # 確保配置文件包含所有必要的設置
            self.ensure_complete_config()

    def _create_default_config(self):
        """創建默認配置文件"""
        # 基本設置
        self.config['file_paths'] = {
            'excel_directory': r'C:\Users\<USER>\OneDrive\LEE\Price ticket and Promotion label\Excel'
        }
        self.config['Settings'] = {
            'printer_name': 'Gprinter GP-1134T'
        }

        # Ethicals 主題設置
        self.config['font_sizes_ethicals'] = {
            'item_description': '50',
            'retail': '50',
            'item_code': '30',
            'rrp': '20',
            'date': '15'
        }
        self.config['text_offsets_ethicals'] = {
            'item_description_offset_x': '10',
            'item_description_offset_y': '30'
        }
        self.config['barcode_options_ethicals'] = {
            'position_offset_y': '65',
            'width_scale': '0.8',
            'height_scale': '1.0'
        }

        # Non-Ethicals 主題設置（更多變數）
        self.config['font_sizes'] = {
            'item_description': '30',
            'retail': '100',
            'rrp': '20',
            'date': '15',
            'item_code': '25'
        }
        self.config['text_offsets'] = {
            'item_description_offset_x': '0',
            'item_description_offset_y': '200',
            'retail_offset_x': '0',
            'retail_offset_y': '80',
            'rrp_offset_x': '-20',
            'rrp_offset_y': '-280',
            'date_offset_x': '20',
            'date_offset_y': '-65',
            'item_code_offset_x': '0',
            'item_code_offset_y': '250'
        }
        self.config['barcode_options'] = {
            'position_offset_x': '0',
            'position_offset_y': '-140',
            'width_scale': '0.8',
            'height_scale': '1.0'
        }
        self.config['label_layout'] = {
            'show_rrp': 'true',
            'show_date': 'true',
            'show_item_code': 'true',
            'background_color': 'white',
            'text_color': 'black',
            'border_width': '2',
            'border_color': 'black'
        }

        with open(self.config_file, 'w', encoding='utf-8') as configfile:
            self.config.write(configfile)

    def get_excel_directory(self):
        """獲取 Excel 目錄路徑"""
        return self.config.get('file_paths', 'excel_directory')

    def get_printer_name(self):
        """獲取打印機名稱"""
        return self.config.get('Settings', 'printer_name')

    def get_ethicals_settings(self):
        """獲取 Ethicals 設置"""
        return {
            'font_sizes': {
                'item_description': self.config.getint('font_sizes_ethicals', 'item_description', fallback=50),
                'retail': self.config.getint('font_sizes_ethicals', 'retail', fallback=50),
                'item_code': self.config.getint('font_sizes_ethicals', 'item_code', fallback=30),
                'rrp': self.config.getint('font_sizes_ethicals', 'rrp', fallback=20),
                'date': self.config.getint('font_sizes_ethicals', 'date', fallback=15)
            },
            'text_offsets': {
                'item_description': {
                    'offset_x': self.config.getint('text_offsets_ethicals', 'item_description_offset_x', fallback=10),
                    'offset_y': self.config.getint('text_offsets_ethicals', 'item_description_offset_y', fallback=30)
                }
            },
            'barcode_options': {
                'position_offset_y': self.config.getint('barcode_options_ethicals', 'position_offset_y', fallback=65),
                'width_scale': self.config.getfloat('barcode_options_ethicals', 'width_scale', fallback=0.8),
                'height_scale': self.config.getfloat('barcode_options_ethicals', 'height_scale', fallback=1.0)
            }
        }

    def get_non_ethicals_settings(self):
        """獲取非 Ethicals 設置（擴展版本）"""
        return {
            'font_sizes': {
                'item_description': self.config.getint('font_sizes', 'item_description', fallback=30),
                'retail': self.config.getint('font_sizes', 'retail', fallback=100),
                'rrp': self.config.getint('font_sizes', 'rrp', fallback=20),
                'date': self.config.getint('font_sizes', 'date', fallback=15),
                'item_code': self.config.getint('font_sizes', 'item_code', fallback=25)
            },
            'text_offsets': {
                'item_description': {
                    'offset_x': self.config.getint('text_offsets', 'item_description_offset_x', fallback=0),
                    'offset_y': self.config.getint('text_offsets', 'item_description_offset_y', fallback=200)
                },
                'retail': {
                    'offset_x': self.config.getint('text_offsets', 'retail_offset_x', fallback=0),
                    'offset_y': self.config.getint('text_offsets', 'retail_offset_y', fallback=80)
                },
                'rrp': {
                    'offset_x': self.config.getint('text_offsets', 'rrp_offset_x', fallback=-20),
                    'offset_y': self.config.getint('text_offsets', 'rrp_offset_y', fallback=-280)
                },
                'date': {
                    'offset_x': self.config.getint('text_offsets', 'date_offset_x', fallback=20),
                    'offset_y': self.config.getint('text_offsets', 'date_offset_y', fallback=-65)
                },
                'item_code': {
                    'offset_x': self.config.getint('text_offsets', 'item_code_offset_x', fallback=0),
                    'offset_y': self.config.getint('text_offsets', 'item_code_offset_y', fallback=250)
                }
            },
            'barcode_options': {
                'position_offset_x': self.config.getint('barcode_options', 'position_offset_x', fallback=0),
                'position_offset_y': self.config.getint('barcode_options', 'position_offset_y', fallback=-140),
                'width_scale': self.config.getfloat('barcode_options', 'width_scale', fallback=0.8),
                'height_scale': self.config.getfloat('barcode_options', 'height_scale', fallback=1.0)
            },
            'label_layout': {
                'show_rrp': self.config.getboolean('label_layout', 'show_rrp', fallback=True),
                'show_date': self.config.getboolean('label_layout', 'show_date', fallback=True),
                'show_item_code': self.config.getboolean('label_layout', 'show_item_code', fallback=True),
                'background_color': self.config.get('label_layout', 'background_color', fallback='white'),
                'text_color': self.config.get('label_layout', 'text_color', fallback='black'),
                'border_width': self.config.getint('label_layout', 'border_width', fallback=2),
                'border_color': self.config.get('label_layout', 'border_color', fallback='black')
            }
        }

    def update_config(self, section, key, value):
        """更新單個配置項"""
        if not self.config.has_section(section):
            self.config.add_section(section)

        self.config.set(section, key, str(value))

        with open(self.config_file, 'w', encoding='utf-8') as configfile:
            self.config.write(configfile)

    def save_settings(self, section, settings):
        """保存設置到配置文件"""
        if not self.config.has_section(section):
            self.config.add_section(section)

        for key, value in settings.items():
            self.config.set(section, key, str(value))

        with open(self.config_file, 'w', encoding='utf-8') as configfile:
            self.config.write(configfile)

    def save_config(self):
        """保存當前配置到文件"""
        try:
            with open(self.config_file, 'w', encoding='utf-8') as configfile:
                self.config.write(configfile)
            return True
        except Exception as e:
            print(f"保存配置文件時發生錯誤: {str(e)}")
            return False

    def ensure_complete_config(self):
        """確保配置文件包含所有必要的設置"""
        required_sections = {
            'font_sizes_ethicals': ['item_description', 'retail', 'item_code', 'rrp', 'date'],
            'text_offsets_ethicals': ['item_description_offset_x', 'item_description_offset_y'],
            'barcode_options_ethicals': ['position_offset_y', 'width_scale', 'height_scale'],
            'font_sizes': ['item_description', 'retail', 'rrp', 'date', 'item_code'],
            'text_offsets': ['item_description_offset_x', 'item_description_offset_y', 'retail_offset_x', 'retail_offset_y', 'rrp_offset_x', 'rrp_offset_y', 'date_offset_x', 'date_offset_y', 'item_code_offset_x', 'item_code_offset_y'],
            'barcode_options': ['position_offset_x', 'position_offset_y', 'width_scale', 'height_scale'],
            'label_layout': ['show_rrp', 'show_date', 'show_item_code', 'background_color', 'text_color', 'border_width', 'border_color']
        }

        config_updated = False

        for section, keys in required_sections.items():
            if not self.config.has_section(section):
                config_updated = True
                break
            for key in keys:
                if not self.config.has_option(section, key):
                    config_updated = True
                    break

        if config_updated:
            print("配置文件不完整，重新創建默認配置...")
            self._create_default_config()
            self.config.read(self.config_file, encoding='utf-8')

    def reload_config(self):
        """重新載入配置文件"""
        self.config.read(self.config_file, encoding='utf-8')
