import configparser
import os

class ConfigManager:
    def __init__(self):
        self.config_file = self._get_config_path()
        self.config = configparser.ConfigParser()
        self.load_config()

    def _get_config_path(self):
        """獲取配置文件的完整路徑"""
        current_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
        return os.path.join(current_dir, 'config.txt')

    def load_config(self):
        """載入配置文件"""
        if not os.path.exists(self.config_file):
            self._create_default_config()
        self.config.read(self.config_file, encoding='utf-8')

    def _create_default_config(self):
        """創建默認配置文件"""
        self.config['file_paths'] = {
            'excel_directory': r'C:\Users\<USER>\OneDrive\LEE\Price ticket and Promotion label\Excel'
        }
        self.config['Settings'] = {
            'printer_name': 'Gprinter GP-1134T'
        }
        
        with open(self.config_file, 'w', encoding='utf-8') as configfile:
            self.config.write(configfile)

    def get_excel_directory(self):
        """獲取 Excel 目錄路徑"""
        return self.config.get('file_paths', 'excel_directory')

    def get_printer_name(self):
        """獲取打印機名稱"""
        return self.config.get('Settings', 'printer_name')

    def get_ethicals_settings(self):
        """獲取 Ethicals 設置"""
        return {
            'font_sizes': {
                'item_description': self.config.getint('font_sizes_ethicals', 'item_description', fallback=50),
                'retail': self.config.getint('font_sizes_ethicals', 'retail', fallback=50),
                'item_code': self.config.getint('font_sizes_ethicals', 'item_code', fallback=30),
                'rrp': self.config.getint('font_sizes_ethicals', 'rrp', fallback=20),
                'date': self.config.getint('font_sizes_ethicals', 'date', fallback=15)
            },
            'text_offsets': {
                'item_description': {
                    'offset_x': self.config.getint('text_offsets_ethicals', 'item_description_offset_x', fallback=10),
                    'offset_y': self.config.getint('text_offsets_ethicals', 'item_description_offset_y', fallback=30)
                }
            },
            'barcode_options': {
                'position_offset_y': self.config.getint('barcode_options_ethicals', 'position_offset_y', fallback=65)
            }
        }

    def get_non_ethicals_settings(self):
        """獲取非 Ethicals 設置"""
        return {
            'font_sizes': {
                'item_description': self.config.getint('font_sizes', 'item_description', fallback=30),
                'retail': self.config.getint('font_sizes', 'retail', fallback=100)
            },
            'text_offsets': {
                'item_description_y': self.config.getint('text_offsets', 'item_description_offset_y', fallback=200),
                'retail_y': self.config.getint('text_offsets', 'retail_offset_y', fallback=80),
                'rrp_x': self.config.getint('text_offsets', 'rrp_offset_x', fallback=-20),
                'rrp_y': self.config.getint('text_offsets', 'rrp_offset_y', fallback=-280),
                'date_x': self.config.getint('text_offsets', 'date_offset_x', fallback=20),
                'date_y': self.config.getint('text_offsets', 'date_offset_y', fallback=-65)
            },
            'barcode_options': {
                'position_offset_y': self.config.getint('barcode_options', 'position_offset_y', fallback=-140)
            }
        }

    def update_config(self, section, key, value):
        """更新單個配置項"""
        if not self.config.has_section(section):
            self.config.add_section(section)

        self.config.set(section, key, str(value))

        with open(self.config_file, 'w', encoding='utf-8') as configfile:
            self.config.write(configfile)

    def save_settings(self, section, settings):
        """保存設置到配置文件"""
        if not self.config.has_section(section):
            self.config.add_section(section)

        for key, value in settings.items():
            self.config.set(section, key, str(value))

        with open(self.config_file, 'w', encoding='utf-8') as configfile:
            self.config.write(configfile)
