import tkinter as tk
from ui.main_window import Ticket<PERSON><PERSON><PERSON>G<PERSON>
from utils.config_manager import ConfigManager
from core.excel_handler import ExcelHandler
from core.printer_manager import PrinterManager

def main():
    try:
        print("初始化配置...")
        config_manager = ConfigManager()
        
        print("初始化 Excel 處理器...")
        excel_handler = ExcelHandler(config_manager)
        
        print("初始化打印管理器...")
        printer_manager = PrinterManager(config_manager)
        
        print("創建主窗口...")
        root = tk.Tk()
        app = TicketGeneratorGUI(
            root=root,
            config_manager=config_manager,
            excel_handler=excel_handler,
            printer_manager=printer_manager
        )
        
        print("啟動應用程序...")
        root.mainloop()
        
    except Exception as e:
        print(f"程序啟動時發生錯誤: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    import os
    import sys
    # 將當前目錄添加到 Python 路徑
    current_dir = os.path.dirname(os.path.abspath(__file__))
    sys.path.insert(0, current_dir)
    main()
