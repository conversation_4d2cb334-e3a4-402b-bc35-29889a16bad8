import tkinter as tk
from tkinter import ttk, messagebox
from ui.preview_panel import PreviewPanel
from ui.search_panel import SearchPanel
from ui.controls_panel import ControlsPanel
from core.excel_handler import ExcelHandler
from core.label_generator import LabelGenerator
from core.printer_manager import PrinterManager
from utils.config_manager import ConfigManager
from utils.logger import Logger
from models.data_models import ProductItem
from PIL import Image, ImageTk
from tkcalendar import DateEntry
import pandas as pd
from datetime import datetime
import os

class TicketGeneratorGUI:
    def __init__(self, root, config_manager, excel_handler, printer_manager):
        self.root = root
        self.config_manager = config_manager
        self.excel_handler = excel_handler
        self.printer_manager = printer_manager
        self.logger = Logger()  # 初始化 logger
        
        # 初始化變量
        self.df = None
        self.original_df = None
        self.search_timer = None
        self.print_enabled = tk.BooleanVar(value=False)
        
        # 創建加載視窗
        self.create_loading_window()
        
        # 初始化主視窗
        self.root.title("藥局標籤生成器")
        self.root.configure(bg='white')
        
        # 創建主框架
        self.main_frame = tk.Frame(self.root, bg='white')
        self.main_frame.pack(fill=tk.BOTH, expand=True)
        
        # 延遲初始化
        self.root.after(100, self.initialize_application)
        
        self.label_generator = LabelGenerator(config_manager)

    def create_loading_window(self):
        """創建加載視窗"""
        self.loading_window = tk.Toplevel(self.root)
        self.loading_window.title("載入中")
        self.loading_window.geometry("300x150")
        self.loading_window.configure(bg='white')
        
        # 確保視窗置中
        screen_width = self.loading_window.winfo_screenwidth()
        screen_height = self.loading_window.winfo_screenheight()
        x = (screen_width - 300) // 2
        y = (screen_height - 150) // 2
        self.loading_window.geometry(f"300x150+{x}+{y}")
        
        # 加載訊息
        self.loading_label = tk.Label(
            self.loading_window,
            text="正在載入...",
            font=("Arial", 12),
            bg='white'
        )
        self.loading_label.pack(pady=20)
        
        # 進度條
        self.progress_bar = ttk.Progressbar(
            self.loading_window,
            mode='indeterminate',
            length=200
        )
        self.progress_bar.pack(pady=10)
        self.progress_bar.start(10)

    def update_excel_preview(self):
        """更新 Excel 預覽數據"""
        try:
            print("更新 Excel 預覽...")
            
            # 清空現有數據
            for item in self.tree.get_children():
                self.tree.delete(item)
            
            if self.df is None:
                print("沒有可用的 Excel 數據")
                return
            
            # 獲取日期範圍
            start_date = pd.to_datetime(self.start_date.get_date())
            end_date = pd.to_datetime(self.end_date.get_date())
            end_date = end_date + pd.Timedelta(days=1) - pd.Timedelta(microseconds=1)
            
            # 過濾數據
            mask = (pd.to_datetime(self.df['Modified']) >= start_date) & \
                   (pd.to_datetime(self.df['Modified']) <= end_date)
            filtered_df = self.df[mask]
            
            # 添加數據到 Treeview
            for _, row in filtered_df.iterrows():
                try:
                    values = (
                        pd.to_datetime(row['Modified']).strftime('%Y-%m-%d %H:%M'),
                        str(row['Item Description']),
                        str(row['Item Code']),
                        f"${float(row['Sale Price']):.2f}" if pd.notnull(row['Sale Price']) else '',
                        f"${float(row['Retail']):.2f}" if pd.notnull(row['Retail']) else '',
                        str(row.get('Alias', ''))
                    )
                    self.tree.insert('', 'end', values=values)
                except Exception as e:
                    print(f"處理行時發生錯誤: {str(e)}")
                    continue
            
            print(f"已更新 Excel 預覽，顯示 {len(filtered_df)} 條記錄")
            
        except Exception as e:
            print(f"更新 Excel 預覽時發生錯誤: {str(e)}")
            import traceback
            traceback.print_exc()

    def initialize_application(self):
        """初始化應用程序"""
        try:
            print("正在初始化應用程序...")
            
            # 初始化UI
            self.initialize_ui()
            
            # 加載 Excel 數據
            if self.excel_handler.load_latest_excel():
                self.df = self.excel_handler.df.copy()
                self.original_df = self.df.copy()
                
                # 設置日期選擇器的初始範圍
                max_date = pd.to_datetime(self.df['Modified']).max()
                min_date = max_date - pd.Timedelta(days=7)  # 預設顯示最近一週的數據
                
                self.start_date.set_date(min_date.date())
                self.end_date.set_date(max_date.date())
                
                # 更新 Excel 預覽
                self.update_excel_preview()
            
            # 關閉加載視窗
            self.loading_window.destroy()
            
            # 顯示主視窗
            self.root.deiconify()
            
            # 聚焦到搜索框
            self.search_entry.focus_set()
            
        except Exception as e:
            print(f"初始化時發生錯誤: {str(e)}")
            import traceback
            traceback.print_exc()

    def initialize_ui(self):
        """初始化UI組件"""
        # 創建左右框架
        self.left_frame = tk.Frame(self.main_frame, bg='white')
        self.right_frame = tk.Frame(self.main_frame, bg='white')
        self.left_frame.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        self.right_frame.pack(side=tk.RIGHT, fill=tk.BOTH, expand=True)
        
        # 創建所有UI組件
        self.create_control_section()
        self.create_search_section()
        self.create_preview_section()
        self.create_adjustment_section()
        self.create_excel_preview()

    def create_control_section(self):
        """創建控制區域"""
        control_frame = tk.LabelFrame(
            self.left_frame,
            text=" 控制區域 ",
            font=("Arial", 12, "bold"),
            bg='white'
        )
        control_frame.pack(fill=tk.X, padx=5, pady=5)
        
        # 列印開關
        print_check = ttk.Checkbutton(
            control_frame,
            text="自動列印標籤",
            variable=self.print_enabled,
            command=self.on_print_toggle
        )
        print_check.pack(side=tk.LEFT, padx=5, pady=5)

    def create_adjustment_section(self):
        """創建調整區域"""
        adj_frame = tk.LabelFrame(
            self.left_frame,
            text=" 標籤調整 ",
            font=("Arial", 12, "bold"),
            bg='white'
        )
        adj_frame.pack(fill=tk.X, padx=5, pady=5)

        # Ethicals 調整控制項
        self.ethicals_adj_frame = tk.Frame(adj_frame, bg='white')

        # 條碼位置調整
        self.create_spinbox(self.ethicals_adj_frame, "條碼位置 (Y):", "barcode_y", -200, 200)

        # 文字位置調整
        self.create_spinbox(self.ethicals_adj_frame, "文字位置 (X):", "text_x", -200, 200)
        self.create_spinbox(self.ethicals_adj_frame, "文字位置 (Y):", "text_y", -200, 200)

        # 字體大小調整
        self.create_spinbox(self.ethicals_adj_frame, "字體大小:", "font_size", 10, 100)

        # Non-Ethicals 調整控制項（更多選項）
        self.non_ethicals_adj_frame = tk.Frame(adj_frame, bg='white')

        # 條碼調整
        self.create_spinbox(self.non_ethicals_adj_frame, "條碼位置 (X):", "barcode_x", -200, 200)
        self.create_spinbox(self.non_ethicals_adj_frame, "條碼位置 (Y):", "barcode_y", -200, 200)
        self.create_spinbox(self.non_ethicals_adj_frame, "條碼寬度比例:", "barcode_width", 50, 150, increment=5)

        # 商品描述調整
        self.create_spinbox(self.non_ethicals_adj_frame, "描述位置 (X):", "desc_x", -200, 200)
        self.create_spinbox(self.non_ethicals_adj_frame, "描述位置 (Y):", "desc_y", -200, 300)
        self.create_spinbox(self.non_ethicals_adj_frame, "描述字體大小:", "desc_font", 10, 80)

        # 零售價調整
        self.create_spinbox(self.non_ethicals_adj_frame, "零售價位置 (X):", "retail_x", -200, 200)
        self.create_spinbox(self.non_ethicals_adj_frame, "零售價位置 (Y):", "retail_y", -200, 300)
        self.create_spinbox(self.non_ethicals_adj_frame, "零售價字體大小:", "retail_font", 20, 150)

        # RRP 調整
        self.create_spinbox(self.non_ethicals_adj_frame, "RRP 位置 (X):", "rrp_x", -200, 200)
        self.create_spinbox(self.non_ethicals_adj_frame, "RRP 位置 (Y):", "rrp_y", -300, 200)
        self.create_spinbox(self.non_ethicals_adj_frame, "RRP 字體大小:", "rrp_font", 10, 50)

        # 日期調整
        self.create_spinbox(self.non_ethicals_adj_frame, "日期位置 (X):", "date_x", -200, 200)
        self.create_spinbox(self.non_ethicals_adj_frame, "日期位置 (Y):", "date_y", -200, 300)
        self.create_spinbox(self.non_ethicals_adj_frame, "日期字體大小:", "date_font", 8, 30)

        # 商品代碼調整
        self.create_spinbox(self.non_ethicals_adj_frame, "代碼位置 (X):", "code_x", -200, 200)
        self.create_spinbox(self.non_ethicals_adj_frame, "代碼位置 (Y):", "code_y", -200, 300)
        self.create_spinbox(self.non_ethicals_adj_frame, "代碼字體大小:", "code_font", 10, 50)

        # 默認隱藏兩個框架
        self.ethicals_adj_frame.pack_forget()
        self.non_ethicals_adj_frame.pack_forget()

    def create_spinbox(self, parent, label_text, name, from_, to, increment=1):
        """創建數值調整框"""
        frame = tk.Frame(parent, bg='white')
        frame.pack(fill=tk.X, padx=5, pady=2)

        tk.Label(
            frame,
            text=label_text,
            font=("Arial", 9),
            bg='white',
            width=18,
            anchor='w'
        ).pack(side=tk.LEFT)

        spinbox = ttk.Spinbox(
            frame,
            from_=from_,
            to=to,
            increment=increment,
            width=8,
            command=lambda: self.on_spinbox_change(name)
        )
        spinbox.pack(side=tk.LEFT, padx=5)

        setattr(self, f"{name}_spinbox", spinbox)

    def create_search_section(self):
        """創建搜索區域"""
        search_frame = tk.Frame(self.left_frame, bg='white')
        search_frame.pack(fill=tk.X, padx=5, pady=5)
        
        tk.Label(
            search_frame,
            text="請輸入商品代碼：",
            font=("Arial", 10),
            bg='white'
        ).pack(side=tk.LEFT)
        
        self.search_entry = ttk.Entry(search_frame, width=30)
        self.search_entry.pack(side=tk.LEFT, padx=5)
        self.search_entry.bind('<KeyRelease>', self.on_search_key_release)

    def create_preview_section(self):
        """創建預覽區域"""
        preview_frame = tk.LabelFrame(
            self.left_frame,
            text=" 預覽 ",
            font=("Arial", 12, "bold"),
            bg='white',
            fg='#2c3e50'
        )
        preview_frame.pack(fill=tk.X, padx=5, pady=5)
        
        # 標籤預覽
        self.preview_label = tk.Label(
            preview_frame,
            text="等待搜尋...",
            bg='white',
            font=("Arial", 12)
        )
        self.preview_label.pack(fill=tk.BOTH, padx=5, pady=5)
        
        # 圖像預覽
        self.image_preview = tk.Label(
            preview_frame,
            bg='white'
        )
        self.image_preview.pack(fill=tk.BOTH, padx=5, pady=5)

    def create_excel_preview(self):
        """創建 Excel 預覽區域"""
        preview_frame = tk.LabelFrame(
            self.right_frame,
            text=" Excel 數據預覽 ",
            font=("Arial", 12, "bold"),
            bg='white'
        )
        preview_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        # 日期選擇區域
        date_frame = tk.Frame(preview_frame, bg='white')
        date_frame.pack(fill=tk.X, padx=5, pady=5)
        
        tk.Label(
            date_frame,
            text="日期範圍：",
            font=("Arial", 10),
            bg='white'
        ).pack(side=tk.LEFT)
        
        self.start_date = DateEntry(
            date_frame,
            width=12,
            background='darkblue',
            foreground='white',
            borderwidth=2
        )
        self.start_date.pack(side=tk.LEFT, padx=5)
        
        tk.Label(
            date_frame,
            text="到",
            font=("Arial", 10),
            bg='white'
        ).pack(side=tk.LEFT)
        
        self.end_date = DateEntry(
            date_frame,
            width=12,
            background='darkblue',
            foreground='white',
            borderwidth=2
        )
        self.end_date.pack(side=tk.LEFT, padx=5)
        
        # 搜索框
        search_frame = tk.Frame(preview_frame, bg='white')
        search_frame.pack(fill=tk.X, padx=5, pady=5)
        
        tk.Label(
            search_frame,
            text="搜索：",
            font=("Arial", 10),
            bg='white'
        ).pack(side=tk.LEFT)
        
        self.excel_search_entry = ttk.Entry(search_frame)
        self.excel_search_entry.pack(side=tk.LEFT, fill=tk.X, expand=True)
        
        # Treeview
        self.tree = ttk.Treeview(
            preview_frame,
            columns=("Modified", "Item Description", "Item Code", 
                    "Sale Price", "Retail", "Alias"),
            show='headings',
            height=20
        )
        
        # 設置列
        columns = {
            "Modified": ("更新時間", 100),
            "Item Description": ("商品名稱", 200),
            "Item Code": ("商品代碼", 100),
            "Sale Price": ("售價", 80),
            "Retail": ("零售價", 80),
            "Alias": ("別名", 100)
        }
        
        for col, (text, width) in columns.items():
            self.tree.heading(col, text=text)
            self.tree.column(col, width=width)
        
        # 滾動條
        scrollbar = ttk.Scrollbar(
            preview_frame,
            orient=tk.VERTICAL,
            command=self.tree.yview
        )
        self.tree.configure(yscrollcommand=scrollbar.set)
        
        self.tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        
        # 綁定事件
        self.tree.bind('<<TreeviewSelect>>', self.on_tree_select)
        self.start_date.bind("<<DateEntrySelected>>", self.apply_date_filter)
        self.end_date.bind("<<DateEntrySelected>>", self.apply_date_filter)
        self.excel_search_entry.bind('<Return>', self.search_excel)

    def on_print_toggle(self):
        """處理列印開關變更"""
        enabled = self.print_enabled.get()
        print(f"列印功能已{'啟用' if enabled else '禁用'}")

    def on_search_key_release(self, event):
        """處理搜索框按鍵釋放事件"""
        if self.search_timer:
            self.root.after_cancel(self.search_timer)
        self.search_timer = self.root.after(500, self.search_and_print)

    def on_tree_select(self, event):
        """處理 Treeview 選擇事件"""
        try:
            selected_items = self.tree.selection()
            if selected_items:
                item_values = self.tree.item(selected_items[0])['values']
                if item_values:
                    # 設置搜索文字
                    self.search_entry.delete(0, tk.END)
                    self.search_entry.insert(0, str(item_values[2]))  # Item Code
                    
                    # 觸發搜索
                    self.search_and_print()
                    
        except Exception as e:
            print(f"選擇項目時發生錯誤: {str(e)}")

    def apply_date_filter(self, event=None):
        """應用日期過濾"""
        try:
            print("\n=== 應用日期過濾 ===")
            
            # 獲取日期範圍
            start_date = pd.to_datetime(self.start_date.get_date())
            end_date = pd.to_datetime(self.end_date.get_date())
            end_date = end_date + pd.Timedelta(days=1) - pd.Timedelta(microseconds=1)
            
            print(f"過濾日期範圍: 從 {start_date} 到 {end_date}")
            
            # 在原始數據中應用日期過濾
            mask = (pd.to_datetime(self.original_df['Modified']) >= start_date) & \
                   (pd.to_datetime(self.original_df['Modified']) <= end_date)
            
            # 獲取搜索文字
            search_text = self.excel_search_entry.get().lower().strip()
            if search_text:
                search_mask = self.original_df.apply(lambda row: any(
                    str(value).lower().find(search_text) != -1 
                    for value in row.values if pd.notnull(value)
                ), axis=1)
                mask = mask & search_mask
            
            # 更新顯示
            self.update_treeview(self.original_df[mask])
            
        except Exception as e:
            print(f"應用日期過濾時發生錯誤: {str(e)}")

    def search_excel(self, event=None):
        """搜索 Excel 數據"""
        try:
            search_text = self.excel_search_entry.get().lower().strip()
            if not search_text:
                self.update_treeview(self.original_df)
                return
            
            # 在所有列中搜索
            mask = self.original_df.apply(lambda row: any(
                str(value).lower().find(search_text) != -1 
                for value in row.values if pd.notnull(value)
            ), axis=1)
            
            self.update_treeview(self.original_df[mask])
            
        except Exception as e:
            print(f"搜索時發生錯誤: {str(e)}")

    def update_treeview(self, df):
        """更新 Treeview 顯示"""
        # 清空現有數據
        for item in self.tree.get_children():
            self.tree.delete(item)
        
        # 添加新數據
        for _, row in df.iterrows():
            try:
                values = (
                    pd.to_datetime(row['Modified']).strftime('%Y-%m-%d %H:%M'),
                    str(row['Item Description']),
                    str(row['Item Code']),
                    f"${float(row['Sale Price']):.2f}" if pd.notnull(row['Sale Price']) else '',
                    f"${float(row['Retail']):.2f}" if pd.notnull(row['Retail']) else '',
                    str(row.get('Alias', ''))
                )
                self.tree.insert('', 'end', values=values)
            except Exception as e:
                print(f"處理行時發生錯誤: {str(e)}")

    def on_spinbox_change(self, name):
        """處理調整框值變更"""
        try:
            # 獲取當前值
            value = getattr(self, f"{name}_spinbox").get()
            print(f"調整 {name}: {value}")

            # 獲取當前商品的部門來決定使用哪套配置
            current_department = getattr(self, 'current_department', 'non-ethicals')
            is_ethicals = current_department.lower() == 'ethicals'

            # 根據部門和參數名稱更新對應的配置
            if is_ethicals:
                self._update_ethicals_config(name, value)
            else:
                self._update_non_ethicals_config(name, value)

            # 重新載入配置並生成預覽
            self.config_manager.reload_config()
            search_text = self.search_entry.get().strip()
            if search_text:
                self.search_and_print()

        except Exception as e:
            print(f"調整值變更時發生錯誤: {str(e)}")

    def _update_ethicals_config(self, name, value):
        """更新 Ethicals 配置"""
        if name == "barcode_y":
            self.config_manager.update_config('barcode_options_ethicals', 'position_offset_y', value)
        elif name == "text_x":
            self.config_manager.update_config('text_offsets_ethicals', 'item_description_offset_x', value)
        elif name == "text_y":
            self.config_manager.update_config('text_offsets_ethicals', 'item_description_offset_y', value)
        elif name == "font_size":
            self.config_manager.update_config('font_sizes_ethicals', 'item_description', value)

    def _update_non_ethicals_config(self, name, value):
        """更新 Non-Ethicals 配置"""
        # 條碼設置
        if name == "barcode_x":
            self.config_manager.update_config('barcode_options', 'position_offset_x', value)
        elif name == "barcode_y":
            self.config_manager.update_config('barcode_options', 'position_offset_y', value)
        elif name == "barcode_width":
            self.config_manager.update_config('barcode_options', 'width_scale', str(float(value) / 100))

        # 商品描述設置
        elif name == "desc_x":
            self.config_manager.update_config('text_offsets', 'item_description_offset_x', value)
        elif name == "desc_y":
            self.config_manager.update_config('text_offsets', 'item_description_offset_y', value)
        elif name == "desc_font":
            self.config_manager.update_config('font_sizes', 'item_description', value)

        # 零售價設置
        elif name == "retail_x":
            self.config_manager.update_config('text_offsets', 'retail_offset_x', value)
        elif name == "retail_y":
            self.config_manager.update_config('text_offsets', 'retail_offset_y', value)
        elif name == "retail_font":
            self.config_manager.update_config('font_sizes', 'retail', value)

        # RRP 設置
        elif name == "rrp_x":
            self.config_manager.update_config('text_offsets', 'rrp_offset_x', value)
        elif name == "rrp_y":
            self.config_manager.update_config('text_offsets', 'rrp_offset_y', value)
        elif name == "rrp_font":
            self.config_manager.update_config('font_sizes', 'rrp', value)

        # 日期設置
        elif name == "date_x":
            self.config_manager.update_config('text_offsets', 'date_offset_x', value)
        elif name == "date_y":
            self.config_manager.update_config('text_offsets', 'date_offset_y', value)
        elif name == "date_font":
            self.config_manager.update_config('font_sizes', 'date', value)

        # 商品代碼設置
        elif name == "code_x":
            self.config_manager.update_config('text_offsets', 'item_code_offset_x', value)
        elif name == "code_y":
            self.config_manager.update_config('text_offsets', 'item_code_offset_y', value)
        elif name == "code_font":
            self.config_manager.update_config('font_sizes', 'item_code', value)

    def search_and_print(self):
        """搜索並列印標籤"""
        try:
            search_text = self.search_entry.get().strip()
            if not search_text:
                return

            # 搜索商品
            items = self.excel_handler.search_items(search_text)
            if items:
                # 取第一個匹配的商品
                item = items[0]

                # 記錄當前商品的部門
                self.current_department = item.department
                print(f"當前商品部門: {self.current_department}")

                # 更新調整控制項的值以反映當前主題設置
                self.update_adjustment_controls(item.department)

                # 生成標籤圖像
                image = self.label_generator.create_label(item)

                # 更新預覽
                self.update_preview(image)

                # 如果啟用了自動列印
                if self.print_enabled.get():
                    self.printer_manager.print_label(image)

        except Exception as e:
            print(f"搜索和列印時發生錯誤: {str(e)}")
            self.logger.error(f"搜索和列印時發生錯誤: {str(e)}")

    def update_adjustment_controls(self, department):
        """根據商品部門更新調整控制項的值"""
        try:
            is_ethicals = department.lower() == 'ethicals'
            print(f"更新調整控制項 - 部門: {department}, 是否為 Ethicals: {is_ethicals}")

            if is_ethicals:
                # 顯示 Ethicals 控制項，隱藏 Non-Ethicals 控制項
                self.ethicals_adj_frame.pack(fill=tk.X, padx=5, pady=5)
                self.non_ethicals_adj_frame.pack_forget()

                # 載入 Ethicals 設置
                settings = self.config_manager.get_ethicals_settings()

                # 更新 spinbox 值
                if hasattr(self, 'barcode_y_spinbox'):
                    self.barcode_y_spinbox.set(settings['barcode_options']['position_offset_y'])
                if hasattr(self, 'text_x_spinbox'):
                    self.text_x_spinbox.set(settings['text_offsets']['item_description']['offset_x'])
                if hasattr(self, 'text_y_spinbox'):
                    self.text_y_spinbox.set(settings['text_offsets']['item_description']['offset_y'])
                if hasattr(self, 'font_size_spinbox'):
                    self.font_size_spinbox.set(settings['font_sizes']['item_description'])

                print("已載入 Ethicals 主題設置")
            else:
                # 顯示 Non-Ethicals 控制項，隱藏 Ethicals 控制項
                self.non_ethicals_adj_frame.pack(fill=tk.X, padx=5, pady=5)
                self.ethicals_adj_frame.pack_forget()

                # 載入 Non-Ethicals 設置
                settings = self.config_manager.get_non_ethicals_settings()

                # 更新所有 Non-Ethicals spinbox 值
                self._update_non_ethicals_spinboxes(settings)

                print("已載入 Non-Ethicals 主題設置")

        except Exception as e:
            print(f"更新調整控制項時發生錯誤: {str(e)}")
            self.logger.error(f"更新調整控制項時發生錯誤: {str(e)}")

    def _update_non_ethicals_spinboxes(self, settings):
        """更新 Non-Ethicals 的所有 spinbox 值"""
        try:
            # 條碼設置
            if hasattr(self, 'barcode_x_spinbox'):
                self.barcode_x_spinbox.set(settings['barcode_options']['position_offset_x'])
            if hasattr(self, 'barcode_y_spinbox'):
                self.barcode_y_spinbox.set(settings['barcode_options']['position_offset_y'])
            if hasattr(self, 'barcode_width_spinbox'):
                self.barcode_width_spinbox.set(int(settings['barcode_options']['width_scale'] * 100))

            # 商品描述設置
            if hasattr(self, 'desc_x_spinbox'):
                self.desc_x_spinbox.set(settings['text_offsets']['item_description']['offset_x'])
            if hasattr(self, 'desc_y_spinbox'):
                self.desc_y_spinbox.set(settings['text_offsets']['item_description']['offset_y'])
            if hasattr(self, 'desc_font_spinbox'):
                self.desc_font_spinbox.set(settings['font_sizes']['item_description'])

            # 零售價設置
            if hasattr(self, 'retail_x_spinbox'):
                self.retail_x_spinbox.set(settings['text_offsets']['retail']['offset_x'])
            if hasattr(self, 'retail_y_spinbox'):
                self.retail_y_spinbox.set(settings['text_offsets']['retail']['offset_y'])
            if hasattr(self, 'retail_font_spinbox'):
                self.retail_font_spinbox.set(settings['font_sizes']['retail'])

            # RRP 設置
            if hasattr(self, 'rrp_x_spinbox'):
                self.rrp_x_spinbox.set(settings['text_offsets']['rrp']['offset_x'])
            if hasattr(self, 'rrp_y_spinbox'):
                self.rrp_y_spinbox.set(settings['text_offsets']['rrp']['offset_y'])
            if hasattr(self, 'rrp_font_spinbox'):
                self.rrp_font_spinbox.set(settings['font_sizes']['rrp'])

            # 日期設置
            if hasattr(self, 'date_x_spinbox'):
                self.date_x_spinbox.set(settings['text_offsets']['date']['offset_x'])
            if hasattr(self, 'date_y_spinbox'):
                self.date_y_spinbox.set(settings['text_offsets']['date']['offset_y'])
            if hasattr(self, 'date_font_spinbox'):
                self.date_font_spinbox.set(settings['font_sizes']['date'])

            # 商品代碼設置
            if hasattr(self, 'code_x_spinbox'):
                self.code_x_spinbox.set(settings['text_offsets']['item_code']['offset_x'])
            if hasattr(self, 'code_y_spinbox'):
                self.code_y_spinbox.set(settings['text_offsets']['item_code']['offset_y'])
            if hasattr(self, 'code_font_spinbox'):
                self.code_font_spinbox.set(settings['font_sizes']['item_code'])

        except Exception as e:
            print(f"更新 Non-Ethicals spinbox 值時發生錯誤: {str(e)}")

    def update_preview(self, image):
        """更新預覽圖像"""
        try:
            if image is None:
                # 清空預覽
                self.image_preview.configure(image='', text="無預覽")
                return

            # 調整圖像大小以適應預覽區域
            preview_width = 300
            preview_height = 225
            resized_image = image.resize((preview_width, preview_height), Image.Resampling.LANCZOS)

            # 轉換為 PhotoImage
            photo = ImageTk.PhotoImage(resized_image)

            # 更新預覽標籤
            self.image_preview.configure(image=photo, text="")
            self.image_preview.image = photo  # 保持引用

            self.logger.info("預覽圖像已更新")
        except Exception as e:
            self.logger.error(f"更新預覽時發生錯誤: {str(e)}")
            self.image_preview.configure(image='', text=f"預覽錯誤: {str(e)}")
            print(f"更新預覽時發生錯誤: {str(e)}")

    def generate_label(self, row):
        """生成標籤"""
        try:
            # 創建 ProductItem 對象
            item = ProductItem.from_df_row(row)
            
            # 生成標籤圖像
            image = self.label_generator.create_label(item)
            
            # 保存標籤
            output_path = os.path.join('output', f'label_{item.item_code}.png')
            os.makedirs('output', exist_ok=True)
            self.label_generator.save_label(image, output_path)
            
            # 更新預覽
            self.update_preview(image)
            
            return output_path
            
        except Exception as e:
            self.logger.error(f"生成標籤時發生錯誤: {str(e)}")
            messagebox.showerror("錯誤", f"生成標籤時發生錯誤: {str(e)}")
            return None

    def run(self):
        """運行應用程序"""
        self.root.mainloop()
