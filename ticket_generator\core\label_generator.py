from PIL import Image, ImageDraw, ImageFont
import barcode
from barcode.writer import ImageWriter
import os
from models.data_models import ProductItem, LabelSettings
from utils.logger import Logger

class LabelGenerator:
    def __init__(self, config_manager, logger=None):
        self.config = config_manager
        self.logger = logger or Logger()
        self.font_path = os.path.join(os.path.dirname(__file__), '..', 'resources', 'fonts', 'arial.ttf')
        self.label_width = 400
        self.label_height = 300
        self.barcode_height = 100
        self.padding = 20

    def create_label(self, item: ProductItem, config_section='default'):
        """創建標籤圖像"""
        try:
            # 創建空白圖像
            image = Image.new('RGB', (self.label_width, self.label_height), 'white')
            draw = ImageDraw.Draw(image)
            
            # 載入字體
            font_size = 24  # 預設字體大小
            font = ImageFont.truetype(self.font_path, font_size)
            
            # 繪製條碼
            barcode_class = barcode.get('code128', item.item_code, writer=ImageWriter())
            barcode_image = barcode_class.render()
            barcode_width = barcode_image.width
            barcode_x = (self.label_width - barcode_width) // 2
            image.paste(barcode_image, (barcode_x, self.padding))
            
            # 繪製商品描述
            description = item.item_description
            description_width = draw.textlength(description, font=font)
            description_x = (self.label_width - description_width) // 2
            draw.text((description_x, self.padding + self.barcode_height + 10), 
                     description, font=font, fill='black')
            
            # 繪製價格
            price_text = f"${item.retail:.2f}"
            if item.rrp and item.rrp > item.retail:
                price_text = f"${item.retail:.2f} RRP ${item.rrp:.2f}"
            
            price_width = draw.textlength(price_text, font=font)
            price_x = (self.label_width - price_width) // 2
            draw.text((price_x, self.padding + self.barcode_height + 40), 
                     price_text, font=font, fill='black')
            
            # 繪製部門
            department = item.department.upper()
            dept_width = draw.textlength(department, font=font)
            dept_x = (self.label_width - dept_width) // 2
            draw.text((dept_x, self.padding + self.barcode_height + 70), 
                     department, font=font, fill='black')
            
            return image
            
        except Exception as e:
            self.logger.error(f"生成標籤時發生錯誤: {str(e)}")
            raise

    def save_label(self, image, output_path):
        """保存標籤圖像"""
        try:
            image.save(output_path)
            self.logger.info(f"標籤已保存到: {output_path}")
            return output_path
        except Exception as e:
            self.logger.error(f"保存標籤時發生錯誤: {str(e)}")
            raise

    def draw_text_centered(self, draw, text, font, width, y_offset):
        """繪製居中文字"""
        try:
            text_width = draw.textlength(text, font=font)
            x = (width - text_width) / 2
            draw.text((x, y_offset), text, font=font, fill="black")
        except Exception as e:
            print(f"繪製居中文字時發生錯誤: {str(e)}")
    
    def add_barcode(self, image, code, y_offset):
        """添加條碼"""
        try:
            # 生成條碼
            code128 = barcode.get('code128', code, writer=ImageWriter())
            barcode_image = code128.render()
            
            # 調整條碼大小
            barcode_width = int(image.width * 0.8)  # 條碼寬度為標籤寬度的 80%
            ratio = barcode_width / barcode_image.width
            barcode_height = int(barcode_image.height * ratio)
            barcode_image = barcode_image.resize((barcode_width, barcode_height))
            
            # 計算條碼位置（水平居中）
            x = (image.width - barcode_image.width) // 2
            
            # 貼上條碼
            image.paste(barcode_image, (x, y_offset))
            
        except Exception as e:
            print(f"添加條碼時發生錯誤: {str(e)}")

    def _draw_description(self, draw, product: ProductItem, settings: LabelSettings):
        """繪製商品描述"""
        # 實現繪製描述的邏輯...

    def _draw_price(self, draw, product: ProductItem, settings: LabelSettings):
        """繪製價格"""
        # 實現繪製價格的邏輯...

    def _draw_barcode(self, image, product: ProductItem, settings: LabelSettings):
        """繪製條碼"""
        # 實現繪製條碼的邏輯...

    def _draw_date(self, draw, product: ProductItem, settings: LabelSettings):
        """繪製日期"""
        # 實現繪製日期的邏輯...
