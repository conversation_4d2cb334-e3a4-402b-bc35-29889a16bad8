from PIL import Image, ImageDraw, ImageFont
import barcode
from barcode.writer import ImageWriter
import os
from models.data_models import ProductItem, LabelSettings
from utils.logger import Logger

class LabelGenerator:
    def __init__(self, config_manager, logger=None):
        self.config = config_manager
        self.logger = logger or Logger()
        # 嘗試使用系統字體，如果找不到則使用默認字體
        self.font_path = self._get_font_path()
        self.label_width = 400
        self.label_height = 300
        self.barcode_height = 100
        self.padding = 20

    def _get_font_path(self):
        """獲取字體路徑"""
        # 嘗試常見的系統字體路徑
        possible_fonts = [
            r'C:\Windows\Fonts\arial.ttf',
            r'C:\Windows\Fonts\calibri.ttf',
            r'C:\Windows\Fonts\tahoma.ttf',
            os.path.join(os.path.dirname(__file__), '..', 'resources', 'fonts', 'arial.ttf')
        ]

        for font_path in possible_fonts:
            if os.path.exists(font_path):
                return font_path

        return None  # 使用默認字體

    def _load_font(self, size):
        """載入指定大小的字體"""
        try:
            if self.font_path and os.path.exists(self.font_path):
                return ImageFont.truetype(self.font_path, size)
            else:
                return ImageFont.load_default()
        except Exception as e:
            self.logger.warning(f"載入字體失敗，使用默認字體: {str(e)}")
            return ImageFont.load_default()

    def create_label(self, item: ProductItem, config_section='default'):
        """創建標籤圖像"""
        try:
            # 創建空白圖像
            image = Image.new('RGB', (self.label_width, self.label_height), 'white')
            draw = ImageDraw.Draw(image)

            # 獲取配置設置
            if item.department.lower() == 'ethicals':
                settings = self.config.get_ethicals_settings()
            else:
                settings = self.config.get_non_ethicals_settings()

            # 載入字體
            font_size = settings['font_sizes'].get('item_description', 24)
            font = self._load_font(font_size)

            # 繪製條碼
            self._draw_barcode(image, item, settings)

            # 繪製商品描述
            self._draw_description(draw, item, settings)

            # 繪製價格
            self._draw_price(draw, item, settings)

            # 繪製日期
            self._draw_date(draw, item, settings)

            # 繪製商品代碼（僅限 non-ethicals）
            if item.department.lower() != 'ethicals':
                self._draw_item_code(draw, item, settings)

            # 繪製邊框
            self._draw_border(draw, settings)

            return image

        except Exception as e:
            self.logger.error(f"生成標籤時發生錯誤: {str(e)}")
            raise

    def save_label(self, image, output_path):
        """保存標籤圖像"""
        try:
            image.save(output_path)
            self.logger.info(f"標籤已保存到: {output_path}")
            return output_path
        except Exception as e:
            self.logger.error(f"保存標籤時發生錯誤: {str(e)}")
            raise

    def draw_text_centered(self, draw, text, font, width, y_offset):
        """繪製居中文字"""
        try:
            text_width = draw.textlength(text, font=font)
            x = (width - text_width) / 2
            draw.text((x, y_offset), text, font=font, fill="black")
        except Exception as e:
            print(f"繪製居中文字時發生錯誤: {str(e)}")
    
    def add_barcode(self, image, code, y_offset):
        """添加條碼"""
        try:
            # 生成條碼
            code128 = barcode.get('code128', code, writer=ImageWriter())
            barcode_image = code128.render()
            
            # 調整條碼大小
            barcode_width = int(image.width * 0.8)  # 條碼寬度為標籤寬度的 80%
            ratio = barcode_width / barcode_image.width
            barcode_height = int(barcode_image.height * ratio)
            barcode_image = barcode_image.resize((barcode_width, barcode_height))
            
            # 計算條碼位置（水平居中）
            x = (image.width - barcode_image.width) // 2
            
            # 貼上條碼
            image.paste(barcode_image, (x, y_offset))
            
        except Exception as e:
            print(f"添加條碼時發生錯誤: {str(e)}")

    def _draw_description(self, draw, product: ProductItem, settings: dict):
        """繪製商品描述"""
        try:
            font_size = settings['font_sizes'].get('item_description', 24)
            font = self._load_font(font_size)

            # 獲取文字偏移
            offset_x = settings['text_offsets'].get('item_description', {}).get('offset_x', 10)
            offset_y = settings['text_offsets'].get('item_description', {}).get('offset_y', 30)

            # 繪製商品描述
            description = product.item_description
            text_width = draw.textlength(description, font=font)
            x = (self.label_width - text_width) // 2 + offset_x
            y = self.padding + self.barcode_height + 10 + offset_y

            draw.text((x, y), description, font=font, fill='black')

        except Exception as e:
            self.logger.error(f"繪製商品描述時發生錯誤: {str(e)}")

    def _draw_price(self, draw, product: ProductItem, settings: dict):
        """繪製價格"""
        try:
            # 零售價字體
            retail_font_size = settings['font_sizes'].get('retail', 50)
            retail_font = self._load_font(retail_font_size)

            # RRP 字體
            rrp_font_size = settings['font_sizes'].get('rrp', 20)
            rrp_font = self._load_font(rrp_font_size)

            # 繪製零售價
            retail_text = f"${product.retail:.2f}"

            # 檢查是否有詳細的偏移設置（non-ethicals）
            if 'retail' in settings['text_offsets'] and isinstance(settings['text_offsets']['retail'], dict):
                # Non-ethicals 設置
                retail_x = settings['text_offsets']['retail']['offset_x']
                retail_y = settings['text_offsets']['retail']['offset_y']
                if retail_x == 0:  # 如果 X 偏移為 0，則居中
                    retail_width = draw.textlength(retail_text, font=retail_font)
                    retail_x = (self.label_width - retail_width) // 2
            else:
                # Ethicals 設置（簡單版本）
                retail_width = draw.textlength(retail_text, font=retail_font)
                retail_x = (self.label_width - retail_width) // 2
                retail_y = settings['text_offsets'].get('retail_y', 80)

            draw.text((retail_x, retail_y), retail_text, font=retail_font, fill='black')

            # 繪製 RRP（如果存在且大於零售價）
            if product.rrp and product.rrp > product.retail:
                # 檢查是否顯示 RRP
                show_rrp = settings.get('label_layout', {}).get('show_rrp', True)
                if show_rrp:
                    rrp_text = f"RRP ${product.rrp:.2f}"

                    if 'rrp' in settings['text_offsets'] and isinstance(settings['text_offsets']['rrp'], dict):
                        # Non-ethicals 設置
                        rrp_x = settings['text_offsets']['rrp']['offset_x']
                        rrp_y = settings['text_offsets']['rrp']['offset_y']
                    else:
                        # Ethicals 設置
                        rrp_x = settings['text_offsets'].get('rrp_x', -20)
                        rrp_y = settings['text_offsets'].get('rrp_y', -280)

                    draw.text((rrp_x, rrp_y), rrp_text, font=rrp_font, fill='black')

        except Exception as e:
            self.logger.error(f"繪製價格時發生錯誤: {str(e)}")

    def _draw_barcode(self, image, product: ProductItem, settings: dict):
        """繪製條碼"""
        try:
            # 生成條碼
            barcode_class = barcode.get('code128', product.item_code, writer=ImageWriter())
            barcode_image = barcode_class.render()

            # 獲取條碼選項
            barcode_options = settings['barcode_options']
            width_scale = barcode_options.get('width_scale', 0.8)
            height_scale = barcode_options.get('height_scale', 1.0)

            # 調整條碼大小
            barcode_width = int(self.label_width * width_scale)
            ratio = barcode_width / barcode_image.width
            barcode_height = int(barcode_image.height * ratio * height_scale)
            barcode_image = barcode_image.resize((barcode_width, barcode_height))

            # 計算條碼位置
            offset_x = barcode_options.get('position_offset_x', 0)
            offset_y = barcode_options.get('position_offset_y', 0)

            barcode_x = (self.label_width - barcode_width) // 2 + offset_x
            barcode_y = self.padding + offset_y

            # 貼上條碼
            image.paste(barcode_image, (barcode_x, barcode_y))

        except Exception as e:
            self.logger.error(f"繪製條碼時發生錯誤: {str(e)}")

    def _draw_date(self, draw, product: ProductItem, settings: dict):
        """繪製日期"""
        try:
            # 檢查是否顯示日期
            show_date = settings.get('label_layout', {}).get('show_date', True)
            if not show_date:
                return

            font_size = settings['font_sizes'].get('date', 15)
            font = self._load_font(font_size)

            # 格式化日期
            date_text = product.modified_date.strftime('%Y-%m-%d')

            # 獲取日期位置
            if 'date' in settings['text_offsets'] and isinstance(settings['text_offsets']['date'], dict):
                # Non-ethicals 設置
                date_x = settings['text_offsets']['date']['offset_x']
                date_y = settings['text_offsets']['date']['offset_y']
            else:
                # Ethicals 設置
                date_x = settings['text_offsets'].get('date_x', 20)
                date_y = settings['text_offsets'].get('date_y', -65)

            draw.text((date_x, date_y), date_text, font=font, fill='black')

        except Exception as e:
            self.logger.error(f"繪製日期時發生錯誤: {str(e)}")

    def _draw_item_code(self, draw, product: ProductItem, settings: dict):
        """繪製商品代碼（僅限 non-ethicals）"""
        try:
            # 檢查是否顯示商品代碼
            show_item_code = settings.get('label_layout', {}).get('show_item_code', True)
            if not show_item_code:
                return

            font_size = settings['font_sizes'].get('item_code', 25)
            font = self._load_font(font_size)

            # 獲取商品代碼位置
            if 'item_code' in settings['text_offsets'] and isinstance(settings['text_offsets']['item_code'], dict):
                item_code_x = settings['text_offsets']['item_code']['offset_x']
                item_code_y = settings['text_offsets']['item_code']['offset_y']

                if item_code_x == 0:  # 如果 X 偏移為 0，則居中
                    code_width = draw.textlength(product.item_code, font=font)
                    item_code_x = (self.label_width - code_width) // 2

                draw.text((item_code_x, item_code_y), product.item_code, font=font, fill='black')

        except Exception as e:
            self.logger.error(f"繪製商品代碼時發生錯誤: {str(e)}")

    def _draw_border(self, draw, settings):
        """繪製標籤邊框"""
        try:
            # 獲取邊框設置，如果沒有設置則使用預設值
            label_layout = settings.get('label_layout', {})
            border_width = label_layout.get('border_width', 2)  # 預設邊框寬度為 2 像素
            border_color = label_layout.get('border_color', 'black')  # 預設邊框顏色為黑色

            # 繪製邊框（矩形）
            # 座標：(左, 上, 右, 下)
            border_coords = [
                0, 0,  # 左上角
                self.label_width - 1, self.label_height - 1  # 右下角
            ]

            # 繪製邊框
            draw.rectangle(border_coords, outline=border_color, width=border_width)

            print(f"已繪製邊框 - 寬度: {border_width}, 顏色: {border_color}")

        except Exception as e:
            self.logger.error(f"繪製邊框時發生錯誤: {str(e)}")
            print(f"繪製邊框時發生錯誤: {str(e)}")
