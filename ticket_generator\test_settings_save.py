#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
測試設置保存功能
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from utils.config_manager import ConfigManager

def test_settings_save():
    """測試設置保存和載入功能"""
    print("=== 測試設置保存功能 ===")
    
    # 創建配置管理器
    config_manager = ConfigManager()
    
    # 測試保存 Non-Ethicals 設置
    print("\n1. 測試保存 Non-Ethicals 設置...")
    
    # 保存一些測試值
    test_values = {
        'item_description': '25',
        'retail': '110', 
        'rrp': '28',
        'date': '18',
        'item_code': '22'
    }
    
    for key, value in test_values.items():
        config_manager.update_config('font_sizes', key, value)
        print(f"  保存字體大小 {key}: {value}")
    
    # 測試保存位置設置
    position_values = {
        'item_description_offset_x': '5',
        'item_description_offset_y': '210',
        'retail_offset_x': '2',
        'retail_offset_y': '125',
        'rrp_offset_x': '265',
        'rrp_offset_y': '8'
    }
    
    for key, value in position_values.items():
        config_manager.update_config('text_offsets', key, value)
        print(f"  保存位置偏移 {key}: {value}")
    
    # 測試保存條碼設置
    barcode_values = {
        'position_offset_x': '2',
        'position_offset_y': '25',
        'width_scale': '0.85',
        'height_scale': '1.1'
    }
    
    for key, value in barcode_values.items():
        config_manager.update_config('barcode_options', key, value)
        print(f"  保存條碼設置 {key}: {value}")
    
    print("\n2. 重新載入配置並驗證...")
    
    # 重新載入配置
    config_manager.reload_config()
    
    # 獲取設置並驗證
    settings = config_manager.get_non_ethicals_settings()
    
    print("  驗證字體大小:")
    for key, expected_value in test_values.items():
        actual_value = str(settings['font_sizes'][key])
        status = "✓" if actual_value == expected_value else "✗"
        print(f"    {status} {key}: 期望 {expected_value}, 實際 {actual_value}")
    
    print("  驗證位置偏移:")
    # 簡化驗證，直接從配置文件讀取
    config_manager.reload_config()
    for key, expected_value in position_values.items():
        actual_value = config_manager.config.get('text_offsets', key, fallback='未找到')
        status = "✓" if actual_value == expected_value else "✗"
        print(f"    {status} {key}: 期望 {expected_value}, 實際 {actual_value}")
    
    print("  驗證條碼設置:")
    for key, expected_value in barcode_values.items():
        actual_value = config_manager.config.get('barcode_options', key, fallback='未找到')
        status = "✓" if actual_value == expected_value else "✗"
        print(f"    {status} {key}: 期望 {expected_value}, 實際 {actual_value}")
    
    print("\n3. 測試 Ethicals 設置...")
    
    # 測試 Ethicals 設置
    ethicals_values = {
        'position_offset_y': '70',
        'item_description_offset_x': '15',
        'item_description_offset_y': '35',
        'item_description': '55'
    }
    
    config_manager.update_config('barcode_options_ethicals', 'position_offset_y', ethicals_values['position_offset_y'])
    config_manager.update_config('text_offsets_ethicals', 'item_description_offset_x', ethicals_values['item_description_offset_x'])
    config_manager.update_config('text_offsets_ethicals', 'item_description_offset_y', ethicals_values['item_description_offset_y'])
    config_manager.update_config('font_sizes_ethicals', 'item_description', ethicals_values['item_description'])
    
    print("  保存 Ethicals 設置完成")
    
    # 重新載入並驗證
    config_manager.reload_config()
    ethicals_settings = config_manager.get_ethicals_settings()
    
    print("  驗證 Ethicals 設置:")
    print(f"    條碼位置 Y: {ethicals_settings['barcode_options']['position_offset_y']}")
    print(f"    描述位置 X: {ethicals_settings['text_offsets']['item_description']['offset_x']}")
    print(f"    描述位置 Y: {ethicals_settings['text_offsets']['item_description']['offset_y']}")
    print(f"    描述字體大小: {ethicals_settings['font_sizes']['item_description']}")
    
    print("\n=== 測試完成 ===")
    print("✓ 設置保存功能正常工作")
    print("✓ 下次啟動應用程序時將使用保存的設置")

if __name__ == "__main__":
    test_settings_save()
