#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
測試條碼高度減少 50% 並成為可調整變數
"""

import sys
import os
from datetime import datetime
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from core.label_generator import LabelGenerator
from models.data_models import ProductItem
from utils.config_manager import Config<PERSON>ana<PERSON>

def test_barcode_height_50percent():
    """測試條碼高度減少 50% 並成為可調整變數"""
    print("=== 測試條碼高度減少 50% 並成為可調整變數 ===")
    
    # 創建配置管理器
    config_manager = ConfigManager()
    
    # 創建標籤生成器
    label_generator = LabelGenerator(config_manager)
    
    # 創建 Ethicals 測試商品
    ethicals_item = ProductItem(
        item_code="123456",
        item_description="PANADOL EXTRA STRENGTH",
        department="ethicals",
        retail=12.99,
        sale_price=12.99,
        rrp=15.99,
        modified_date=datetime(2025, 1, 7)
    )
    
    # 創建 Non-Ethicals 測試商品
    non_ethicals_item = ProductItem(
        item_code="028234",
        item_description="CODRAL ORIG DAY/NIGHT 24 TAB",
        department="PANADOL",
        retail=18.99,
        sale_price=18.99,
        rrp=25.04,
        modified_date=datetime(2025, 1, 7)
    )
    
    print("測試商品:")
    print(f"  Ethicals: {ethicals_item.item_description}")
    print(f"  Non-Ethicals: {non_ethicals_item.item_description}")
    
    # 檢查當前設置
    print(f"\n--- 檢查當前設置 ---")
    config_manager.reload_config()
    
    ethicals_settings = config_manager.get_ethicals_settings()
    non_ethicals_settings = config_manager.get_non_ethicals_settings()
    
    print("Ethicals 條碼設置:")
    print(f"  高度比例: {ethicals_settings['barcode_options']['height_scale']} (應該是 0.5)")
    print(f"  寬度比例: {ethicals_settings['barcode_options']['width_scale']}")
    
    print("Non-Ethicals 條碼設置:")
    print(f"  高度比例: {non_ethicals_settings['barcode_options']['height_scale']} (應該是 0.5)")
    print(f"  寬度比例: {non_ethicals_settings['barcode_options']['width_scale']}")
    
    # 生成基準標籤（50% 高度）
    print(f"\n--- 生成基準標籤（50% 高度）---")
    try:
        # Ethicals 標籤
        image = label_generator.create_label(ethicals_item)
        image.save("test_ethicals_50percent_height.png")
        print("✓ 已生成 Ethicals 50% 高度標籤: test_ethicals_50percent_height.png")
        
        # Non-Ethicals 標籤
        image = label_generator.create_label(non_ethicals_item)
        image.save("test_non_ethicals_50percent_height.png")
        print("✓ 已生成 Non-Ethicals 50% 高度標籤: test_non_ethicals_50percent_height.png")
        
    except Exception as e:
        print(f"✗ 生成基準標籤時發生錯誤: {str(e)}")
    
    # 測試 Ethicals 條碼高度調整
    print(f"\n--- 測試 Ethicals 條碼高度調整 ---")
    height_values = [0.3, 0.5, 0.7, 1.0]
    
    for height in height_values:
        print(f"  測試高度比例: {height}")
        config_manager.update_config('barcode_options_ethicals', 'height_scale', str(height))
        config_manager.reload_config()
        
        try:
            image = label_generator.create_label(ethicals_item)
            image.save(f"test_ethicals_height_var_{height}.png")
            print(f"    ✓ 已生成: test_ethicals_height_var_{height}.png")
        except Exception as e:
            print(f"    ✗ 錯誤: {str(e)}")
    
    # 測試 Non-Ethicals 條碼高度調整
    print(f"\n--- 測試 Non-Ethicals 條碼高度調整 ---")
    
    for height in height_values:
        print(f"  測試高度比例: {height}")
        config_manager.update_config('barcode_options', 'height_scale', str(height))
        config_manager.reload_config()
        
        try:
            image = label_generator.create_label(non_ethicals_item)
            image.save(f"test_non_ethicals_height_var_{height}.png")
            print(f"    ✓ 已生成: test_non_ethicals_height_var_{height}.png")
        except Exception as e:
            print(f"    ✗ 錯誤: {str(e)}")
    
    # 恢復 50% 高度設置
    print(f"\n--- 恢復 50% 高度設置 ---")
    config_manager.update_config('barcode_options_ethicals', 'height_scale', '0.5')
    config_manager.update_config('barcode_options', 'height_scale', '0.5')
    config_manager.reload_config()
    
    print("✓ 已恢復所有條碼高度為 50%")
    
    # 最終驗證
    print(f"\n--- 最終驗證 ---")
    ethicals_settings = config_manager.get_ethicals_settings()
    non_ethicals_settings = config_manager.get_non_ethicals_settings()
    
    print("最終設置:")
    print(f"  Ethicals 高度比例: {ethicals_settings['barcode_options']['height_scale']}")
    print(f"  Non-Ethicals 高度比例: {non_ethicals_settings['barcode_options']['height_scale']}")
    
    print("\n=== 測試完成 ===")
    print("✅ 條碼高度已減少 50% 並成為可調整變數")
    print("📋 功能總結:")
    print("  • Ethicals 條碼高度：可調整 (40-120%)")
    print("  • Non-Ethicals 條碼高度：可調整 (30-120%)")
    print("  • 默認高度：50% (減半)")
    print("  • 所有調整都會自動保存")
    print("  • 可在應用程序界面中調整")

if __name__ == "__main__":
    test_barcode_height_50percent()
