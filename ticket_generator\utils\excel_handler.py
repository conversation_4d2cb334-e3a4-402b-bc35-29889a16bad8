import pandas as pd
import os
import glob

class ExcelHandler:
    def __init__(self, config_manager):
        self.config = config_manager
        self.df = None
        self.current_excel_path = None
        self.load_latest_excel()
    
    def load_latest_excel(self):
        """加載最新的 Excel 文件"""
        try:
            excel_dir = self.config.get_excel_directory()
            if not os.path.exists(excel_dir):
                raise FileNotFoundError(f"Excel 目錄不存在：{excel_dir}")
            
            excel_files = glob.glob(os.path.join(excel_dir, "*.xlsx"))
            if not excel_files:
                raise FileNotFoundError(f"在 {excel_dir} 中找不到 Excel 文件")
            
            latest_excel = max(excel_files, key=os.path.getmtime)
            self.current_excel_path = latest_excel
            
            self.df = pd.read_excel(latest_excel)
            self.df['Modified'] = pd.to_datetime(self.df['Modified'])
            
            return True
            
        except Exception as e:
            print(f"加載 Excel 文件時發生錯誤：{str(e)}")
            return False
    
    def search_item(self, search_text):
        """搜索商品"""
        if self.df is None:
            return None
        
        try:
            match = self.df[
                self.df['Item Code'].astype(str).str.contains(search_text, case=False) |
                self.df['Item Description'].astype(str).str.contains(search_text, case=False)
            ]
            
            return match.iloc[0] if not match.empty else None
            
        except Exception as e:
            print(f"搜索時發生錯誤：{str(e)}")
            return None 