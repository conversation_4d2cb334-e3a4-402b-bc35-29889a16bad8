#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
測試條碼大小調整
"""

import sys
import os
from datetime import datetime
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from core.label_generator import LabelGenerator
from models.data_models import ProductItem
from utils.config_manager import ConfigManager

def test_barcode_size():
    """測試不同條碼大小設置"""
    print("=== 測試條碼大小調整 ===")
    
    # 創建配置管理器
    config_manager = ConfigManager()

    # 創建標籤生成器
    label_generator = LabelGenerator(config_manager)
    
    # 創建測試商品
    test_item = ProductItem(
        item_code="028234",
        item_description="CODRAL ORIG DAY/NIGHT 24 TAB",
        department="PANADOL",  # Non-Ethicals
        retail=18.99,
        sale_price=18.99,
        rrp=25.04,
        modified_date=datetime(2025, 1, 7)
    )
    
    print(f"測試商品: {test_item.item_description}")
    print(f"商品代碼: {test_item.item_code}")
    print(f"零售價: ${test_item.retail:.2f}")
    print(f"RRP: ${test_item.rrp:.2f}")
    
    # 測試不同的條碼寬度設置
    width_scales = [0.3, 0.5, 0.7, 1.0]
    
    for i, width_scale in enumerate(width_scales):
        print(f"\n--- 測試條碼寬度比例: {width_scale} ---")
        
        # 更新條碼寬度設置
        config_manager.update_config('barcode_options', 'width_scale', str(width_scale))
        
        # 重新載入配置
        config_manager.reload_config()
        
        try:
            # 生成標籤
            image = label_generator.create_label(test_item)
            
            # 保存測試圖像
            output_path = f"test_barcode_width_{width_scale}.png"
            image.save(output_path)
            print(f"✓ 已生成測試標籤: {output_path}")
            
        except Exception as e:
            print(f"✗ 生成標籤時發生錯誤: {str(e)}")
    
    # 恢復推薦的設置（較小的條碼）
    print(f"\n--- 設置推薦的條碼大小 ---")
    config_manager.update_config('barcode_options', 'width_scale', '0.5')
    config_manager.update_config('barcode_options', 'position_offset_y', '20')
    print("✓ 已設置條碼寬度比例為 0.5（推薦大小）")
    
    # 生成最終標籤
    try:
        config_manager.reload_config()
        image = label_generator.create_label(test_item)
        image.save("final_label_small_barcode.png")
        print("✓ 已生成最終標籤: final_label_small_barcode.png")
    except Exception as e:
        print(f"✗ 生成最終標籤時發生錯誤: {str(e)}")
    
    print("\n=== 測試完成 ===")
    print("📋 建議:")
    print("  • 條碼寬度比例 0.5 提供良好的平衡")
    print("  • 您可以在應用程序中使用 '條碼寬度比例' 調整項來微調")
    print("  • 範圍: 50-150 (對應 0.5-1.5 的比例)")

if __name__ == "__main__":
    test_barcode_size()
