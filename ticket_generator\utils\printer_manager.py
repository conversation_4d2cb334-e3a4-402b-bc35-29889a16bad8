import win32print
import win32ui
from PIL import ImageWin

class PrinterManager:
    def __init__(self, config_manager):
        self.config = config_manager
        self.printer_name = self.config.get_printer_name()
    
    def print_label(self, image_path):
        """列印標籤"""
        try:
            # 打開打印機
            hprinter = win32print.OpenPrinter(self.printer_name)
            
            try:
                # 創建打印機 DC
                hdc = win32ui.CreateDC()
                hdc.CreatePrinterDC(self.printer_name)
                
                # 開始打印作業
                hdc.StartDoc(image_path)
                hdc.StartPage()
                
                # 打印圖像
                image = Image.open(image_path)
                dib = ImageWin.Dib(image)
                dib.draw(hdc.GetHandleOutput(), (0, 0, image.size[0], image.size[1]))
                
                # 結束打印作業
                hdc.EndPage()
                hdc.EndDoc()
                hdc.DeleteDC()
                
                return True
                
            finally:
                win32print.ClosePrinter(hprinter)
                
        except Exception as e:
            print(f"列印標籤時發生錯誤：{str(e)}")
            return False 